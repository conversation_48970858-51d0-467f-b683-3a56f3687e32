"""
Ezviz 面板灰度命令 - 专门用于面板灰度操作。
"""
import logging
from typing import List, Optional

import click

from ledvance_cli import config as app_config
from ledvance_cli.core import ezviz_api
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.utils import format_output, show_progress

logger = logging.getLogger(__name__)


@click.command("ezviz-product-diff")
@click.option(
    "--keyword",
    default="LDV_",
    type=str,
    help="产品关键字。"
)
@click.pass_context
def ezviz_panel_gray(
    ctx: click.Context,
    keyword: str
):
    """
    Ezviz 产品比较。

    此命令支持：
    1. 比较生产环境和测试环境产品差异。
    """
    is_dry_run = ctx.obj.dry_run
    output_format = ctx.obj.output_format

    logger.info(f"开始标记不同环境的产品差异 (keyword: {keyword})")

    try:
        base_payload = {
            'keyword': keyword
        }
        prod_env = 'Prod'
        test_env = 'Test'
        prod_products = ezviz_api.get_all_product(base_payload, prod_env)
        test_products = ezviz_api.get_all_product(base_payload, test_env)
        prod_dict = {p['productId']: p for p in prod_products}
        test_dict = {p['productId']: p for p in test_products}
        all_product_ids = set(prod_dict.keys()) | set(test_dict.keys())
        res = []
        for pid in all_product_ids:
            if pid not in test_dict:
                prod_dict[pid]['category']
                res.append({'env': 'Test','category': prod_dict[pid]['category'], 'pid': pid})
                continue
            if pid not in prod_dict:
                res.append({'env': 'Prod','category': test_dict[pid]['category'], 'pid': pid})
                continue
        
        format_output(res, output_format, title=f"产品比较结果")

    except CLIError as e:
        logger.error(f"产品比较时出错: {e}")
        ctx.exit(1)
    except Exception as e:
        logger.error(f"发生意外错误: {e}", exc_info=True)
        ctx.exit(1)

    logger.info("产品比较操作完成。")
